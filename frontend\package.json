{"name": "veslint-frontend", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@supabase/supabase-js": "^2.39.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "axios": "^1.6.2", "chart.js": "^4.4.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "firebase": "^10.7.0", "framer-motion": "^10.16.5", "next": "14.0.3", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "tailwind-merge": "^2.1.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.1", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.3.2"}}