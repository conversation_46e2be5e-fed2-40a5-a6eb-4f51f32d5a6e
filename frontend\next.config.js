/** @type {import('next').NextConfig} */

// =============================================================================
// VESLINT Next.js Configuration
// =============================================================================
// Professional Next.js configuration for the VESLINT maritime vessel
// classification frontend. Optimized for Vercel deployment and production use.
// =============================================================================

const path = require('path');

// Environment-specific configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Build configuration
const nextConfig = {
  // =============================================================================
  // Core Configuration
  // =============================================================================
  
  // React strict mode for better development experience
  reactStrictMode: true,
  
  // SWC minifier for better performance
  swcMinify: true,
  
  // TypeScript configuration
  typescript: {
    // Fail build on TypeScript errors in production
    ignoreBuildErrors: false,
  },
  
  // ESLint configuration
  eslint: {
    // Fail build on ESLint errors in production
    ignoreDuringBuilds: false,
    dirs: ['src', 'pages', 'components', 'lib', 'utils'],
  },
  
  // =============================================================================
  // Performance & Optimization
  // =============================================================================
  
  // Experimental features for better performance
  experimental: {
    // Enable modern JavaScript features
    esmExternals: true,
    
    // Optimize server-side rendering
    serverComponentsExternalPackages: ['@supabase/supabase-js'],
    
    // Optimize bundle analysis
    bundlePagesExternals: true,
  },
  
  // Image optimization
  images: {
    // Optimize images for performance
    formats: ['image/webp', 'image/avif'],
    
    // Image domains for external images
    domains: [
      'firebasestorage.googleapis.com',
      'lh3.googleusercontent.com', // Google profile pictures
      'avatars.githubusercontent.com', // GitHub avatars
      'images.unsplash.com', // Stock images
    ],
    
    // Device sizes for responsive images
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    
    // Image sizes for different breakpoints
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    
    // Minimize CLS (Cumulative Layout Shift)
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
  },
  
  // Compiler optimizations
  compiler: {
    // Remove console logs in production
    removeConsole: isProduction ? {
      exclude: ['error', 'warn'],
    } : false,
    
    // Styled-components support (if using @emotion/styled)
    styledComponents: true,
  },
  
  // =============================================================================
  // Build Configuration
  // =============================================================================
  
  // Output configuration
  output: 'standalone', // Optimized for Vercel deployment
  
  // Generate source maps in development
  productionBrowserSourceMaps: isDevelopment,
  
  // Optimize static generation
  trailingSlash: false,
  
  // Build ID for cache busting
  generateBuildId: async () => {
    // Use git commit hash if available, otherwise use timestamp
    const { execSync } = require('child_process');
    try {
      const gitHash = execSync('git rev-parse HEAD').toString().trim().slice(0, 8);
      return `veslint-${gitHash}`;
    } catch {
      return `veslint-${Date.now()}`;
    }
  },
  
  // =============================================================================
  // Routing & Redirects
  // =============================================================================
  
  // Custom redirects for better UX
  async redirects() {
    return [
      // Redirect root to dashboard for authenticated users
      {
        source: '/',
        has: [
          {
            type: 'cookie',
            key: 'firebase-auth-token',
          },
        ],
        destination: '/dashboard',
        permanent: false,
      },
      
      // Legacy route redirects (if migrating from old URLs)
      {
        source: '/analysis',
        destination: '/new-analysis',
        permanent: true,
      },
      
      // Redirect old result URLs
      {
        source: '/result/:jobId',
        destination: '/results/:jobId',
        permanent: true,
      },
    ];
  },
  
  // Custom rewrites for API routes
  async rewrites() {
    return [
      // Proxy API requests to backend
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/v1/:path*`,
      },
      
      // Health check endpoint
      {
        source: '/health',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/health`,
      },
    ];
  },
  
  // Custom headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          // Security headers
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
          
          // Performance headers
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
        ],
      },
      
      // Cache static assets
      {
        source: '/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      
      // Cache API responses briefly
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=60, s-maxage=60',
          },
        ],
      },
    ];
  },
  
  // =============================================================================
  // Webpack Configuration
  // =============================================================================
  
  webpack: (config, { buildId, dev, isServer, defaultLoaders, nextRuntime, webpack }) => {
    // Add custom webpack configurations
    
    // Resolve aliases for cleaner imports
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname, 'src'),
      '@/components': path.resolve(__dirname, 'src/components'),
      '@/lib': path.resolve(__dirname, 'src/lib'),
      '@/utils': path.resolve(__dirname, 'src/utils'),
      '@/hooks': path.resolve(__dirname, 'src/hooks'),
      '@/types': path.resolve(__dirname, 'src/types'),
      '@/styles': path.resolve(__dirname, 'src/styles'),
    };
    
    // Optimize bundle size
    if (!dev && !isServer) {
      // Analyze bundle in production
      if (process.env.ANALYZE === 'true') {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'server',
            analyzerPort: 8888,
            openAnalyzer: true,
          })
        );
      }
      
      // Split vendor chunks for better caching
      config.optimization.splitChunks = {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks.cacheGroups,
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            maxSize: 244000, // ~250KB
          },
          firebase: {
            test: /[\\/]node_modules[\\/]firebase[\\/]/,
            name: 'firebase',
            chunks: 'all',
            priority: 10,
          },
          mui: {
            test: /[\\/]node_modules[\\/]@mui[\\/]/,
            name: 'mui',
            chunks: 'all',
            priority: 10,
          },
          recharts: {
            test: /[\\/]node_modules[\\/]recharts[\\/]/,
            name: 'recharts',
            chunks: 'all',
            priority: 10,
          },
        },
      };
    }
    
    // Handle CSS modules
    config.module.rules.push({
      test: /\.module\.css$/,
      use: [
        defaultLoaders.css,
        {
          loader: 'css-loader',
          options: {
            modules: {
              localIdentName: dev
                ? '[name]__[local]__[hash:base64:5]'
                : '[hash:base64:8]',
            },
          },
        },
      ],
    });
    
    // Ignore source maps for certain packages in production
    if (!dev) {
      config.ignoreWarnings = [
        /Failed to parse source map/,
        /Critical dependency: the request of a dependency is an expression/,
      ];
    }
    
    return config;
  },
  
  // =============================================================================
  // Environment Variables
  // =============================================================================
  
  env: {
    // Build-time environment variables
    BUILD_TIME: new Date().toISOString(),
    BUILD_ID: process.env.VERCEL_GIT_COMMIT_SHA || 'local',
    APP_VERSION: process.env.npm_package_version || '1.0.0',
  },
  
  // Public runtime config (available on client and server)
  publicRuntimeConfig: {
    // These will be available via getConfig() on both client and server
    apiUrl: process.env.NEXT_PUBLIC_API_URL,
    appName: 'VESLINT',
    version: process.env.npm_package_version || '1.0.0',
  },
  
  // Server runtime config (only available on server)
  serverRuntimeConfig: {
    // These will only be available on the server side
    internalApiUrl: process.env.INTERNAL_API_URL,
    secretKey: process.env.SECRET_KEY,
  },
  
  // =============================================================================
  // Development Configuration
  // =============================================================================
  
  ...(isDevelopment && {
    // Development-specific configuration
    onDemandEntries: {
      // Period (in ms) where the server will keep pages in the buffer
      maxInactiveAge: 25 * 1000,
      // Number of pages that should be kept simultaneously without being disposed
      pagesBufferLength: 2,
    },
    
    // Fast refresh configuration
    fastRefresh: true,
  }),
  
  // =============================================================================
  // Production Configuration
  // =============================================================================
  
  ...(isProduction && {
    // Production-specific configuration
    compress: true,
    
    // Static optimization
    exportPathMap: undefined, // Use default behavior
    
    // Asset optimization
    assetPrefix: process.env.CDN_URL || '',
    
    // Additional production optimizations
    cleanDistDir: true,
    
    // Optimize fonts
    optimizeFonts: true,
  }),
  
  // =============================================================================
  // Vercel-specific Configuration
  // =============================================================================
  
  // Vercel deployment configuration
  ...(process.env.VERCEL && {
    // Vercel-specific settings
    target: 'server',
    
    // Edge runtime for specific routes
    experimental: {
      ...nextConfig.experimental,
      edge: true,
    },
  }),
  
  // =============================================================================
  // Monitoring & Analytics
  // =============================================================================
  
  // Analytics configuration
  analytical: {
    // Enable Next.js analytics
    google: {
      trackingId: process.env.NEXT_PUBLIC_GA_TRACKING_ID,
    },
  },
  
  // =============================================================================
  // PWA Configuration (if needed)
  // =============================================================================
  
  // PWA settings (uncomment if you want PWA support)
  // pwa: {
  //   dest: 'public',
  //   register: true,
  //   skipWaiting: true,
  //   disable: isDevelopment,
  // },
};

// =============================================================================
// Plugin Configuration
// =============================================================================

// Add webpack-bundle-analyzer in development
if (process.env.ANALYZE === 'true') {
  const withBundleAnalyzer = require('@next/bundle-analyzer')({
    enabled: true,
  });
  module.exports = withBundleAnalyzer(nextConfig);
} else {
  module.exports = nextConfig;
}

// =============================================================================
// Configuration Validation
// =============================================================================

// Validate required environment variables
const requiredEnvVars = [
  'NEXT_PUBLIC_FIREBASE_API_KEY',
  'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN',
  'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
];

if (isProduction) {
  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingEnvVars.forEach(envVar => {
      console.error(`   ${envVar}`);
    });
    console.error('\nPlease check your environment configuration.');
    process.exit(1);
  }
}

// Log configuration summary
if (isDevelopment) {
  console.log('🚀 VESLINT Next.js Configuration Loaded');
  console.log(`   Environment: ${process.env.NODE_ENV}`);
  console.log(`   TypeScript: ${nextConfig.typescript ? 'Enabled' : 'Disabled'}`);
  console.log(`   SWC Minify: ${nextConfig.swcMinify ? 'Enabled' : 'Disabled'}`);
  console.log(`   App Directory: ${nextConfig.experimental?.appDir ? 'Enabled' : 'Disabled'}`);
}