/** @type {import('tailwindcss').Config} */

// =============================================================================
// VESLINT Tailwind CSS Configuration
// =============================================================================
// Professional Tailwind CSS configuration for the VESLINT maritime vessel
// classification frontend. Designed to work alongside Material-UI components
// while providing additional utility classes and maritime-themed design tokens.
// =============================================================================

const colors = require('tailwindcss/colors');
const { fontFamily } = require('tailwindcss/defaultTheme');

module.exports = {
  // =============================================================================
  // Content Configuration
  // =============================================================================
  
  content: [
    // Next.js app directory (Next.js 13+)
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    
    // Pages directory (if using pages router)
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    
    // Components
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    
    // Hooks and utilities
    './src/hooks/**/*.{js,ts,jsx,tsx}',
    './src/utils/**/*.{js,ts,jsx,tsx}',
    './src/lib/**/*.{js,ts,jsx,tsx}',
    
    // Root app files
    './src/**/*.{js,ts,jsx,tsx,mdx}',
    
    // Public HTML files
    './public/**/*.html',
    
    // MDX files for documentation
    './docs/**/*.{md,mdx}',
  ],
  
  // =============================================================================
  // Dark Mode Configuration
  // =============================================================================
  
  darkMode: 'class', // Use class-based dark mode for compatibility with Material-UI
  
  // =============================================================================
  // Theme Extension
  // =============================================================================
  
  theme: {
    extend: {
      // =============================================================================
      // Color Palette - Maritime Theme
      // =============================================================================
      
      colors: {
        // Brand colors inspired by maritime themes
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
          950: '#082f49',
        },
        
        // Secondary colors - Ocean greens
        secondary: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
          950: '#020617',
        },
        
        // Accent colors - Maritime orange/amber
        accent: {
          50: '#fffbeb',   // Very light amber
          100: '#fef3c7',  // Light amber
          200: '#fde68a',  // Lighter amber
          300: '#fcd34d',  // Light-medium amber
          400: '#fbbf24',  // Medium amber
          500: '#f59e0b',  // Primary amber
          600: '#d97706',  // Darker amber
          700: '#b45309',  // Dark amber
          800: '#92400e',  // Very dark amber
          900: '#78350f',  // Deepest amber
          950: '#451a03',  // Darkest amber
        },
        
        // Neutral colors with maritime feel
        neutral: {
          50: '#fafafa',   // Very light gray
          100: '#f5f5f5',  // Light gray
          200: '#e5e5e5',  // Lighter gray
          300: '#d4d4d4',  // Light-medium gray
          400: '#a3a3a3',  // Medium gray
          500: '#737373',  // Primary gray
          600: '#525252',  // Darker gray
          700: '#404040',  // Dark gray
          800: '#262626',  // Very dark gray
          900: '#171717',  // Deepest gray
          950: '#0a0a0a',  // Darkest gray
        },
        
        // Status colors
        success: colors.emerald,
        warning: colors.amber,
        error: colors.red,
        info: colors.blue,
        
        // Maritime-specific colors
        maritime: {
          'ocean-deep': '#0f172a',     // Deep ocean blue
          'ocean-surface': '#1e40af',  // Ocean surface blue
          'wave-foam': '#e0f2fe',      // Wave foam white-blue
          'ship-hull': '#374151',      // Ship hull gray
          'port-light': '#059669',     // Port navigation green
          'starboard-light': '#dc2626', // Starboard navigation red
          'lighthouse': '#fbbf24',     // Lighthouse beacon amber
          'anchor': '#6b7280',         // Anchor chain gray
          'sail': '#f8fafc',          // Sail white
        },
        
        // Background colors
        background: {
          DEFAULT: '#ffffff',
          dark: '#0f172a',
          muted: '#f8fafc',
          'muted-dark': '#1e293b',
        },
        
        // Border colors
        border: {
          DEFAULT: '#e2e8f0',
          dark: '#334155',
          muted: '#f1f5f9',
          'muted-dark': '#475569',
        },
      },
      
      // =============================================================================
      // Typography
      // =============================================================================
      
      fontFamily: {
        sans: ['var(--font-inter)'],
        mono: ['JetBrains Mono', 'Fira Code', ...fontFamily.mono],
        display: ['Poppins', 'Inter', ...fontFamily.sans],
        heading: ['Poppins', 'Inter', ...fontFamily.sans],
      },
      
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
      },
      
      fontWeight: {
        thin: '100',
        extralight: '200',
        light: '300',
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
        extrabold: '800',
        black: '900',
      },
      
      // =============================================================================
      // Spacing & Sizing
      // =============================================================================
      
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '100': '25rem',
        '112': '28rem',
        '128': '32rem',
        '144': '36rem',
        '160': '40rem',
        '176': '44rem',
        '192': '48rem',
        '208': '52rem',
        '224': '56rem',
        '240': '60rem',
        '256': '64rem',
      },
      
      maxWidth: {
        '8xl': '88rem',
        '9xl': '96rem',
      },
      
      // =============================================================================
      // Border Radius
      // =============================================================================
      
      borderRadius: {
        'xs': '0.125rem',
        '4xl': '2rem',
        '5xl': '2.5rem',
        '6xl': '3rem',
      },
      
      // =============================================================================
      // Box Shadow
      // =============================================================================
      
      boxShadow: {
        // Elevation shadows
        'elevation-1': '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
        'elevation-2': '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
        'elevation-3': '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
        'elevation-4': '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)',
        'elevation-5': '0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22)',
        
        // Maritime-themed shadows
        'wave': '0 4px 14px 0 rgba(59, 130, 246, 0.15)',
        'ocean': '0 8px 30px 0 rgba(30, 64, 175, 0.2)',
        'depth': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        
        // Card shadows
        'card': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'card-hover': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'card-focus': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
      
      // =============================================================================
      // Animations & Transitions
      // =============================================================================
      
      animation: {
        // Loading animations
        'spin-slow': 'spin 3s linear infinite',
        'pulse-slow': 'pulse 3s ease-in-out infinite',
        'bounce-subtle': 'bounce 2s ease-in-out infinite',
        
        // Maritime-themed animations
        'wave': 'wave 2s ease-in-out infinite',
        'float': 'float 3s ease-in-out infinite',
        'drift': 'drift 4s ease-in-out infinite',
        
        // UI animations
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'fade-out': 'fadeOut 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'scale-out': 'scaleOut 0.2s ease-out',
      },
      
      keyframes: {
        wave: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
          '33%': { transform: 'translateY(-5px) rotate(1deg)' },
          '66%': { transform: 'translateY(-2px) rotate(-1deg)' },
        },
        drift: {
          '0%, 100%': { transform: 'translateX(0px) translateY(0px)' },
          '25%': { transform: 'translateX(2px) translateY(-2px)' },
          '50%': { transform: 'translateX(-1px) translateY(-4px)' },
          '75%': { transform: 'translateX(-2px) translateY(-1px)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0px)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0px)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        scaleOut: {
          '0%': { transform: 'scale(1)', opacity: '1' },
          '100%': { transform: 'scale(0.95)', opacity: '0' },
        },
      },
      
      // =============================================================================
      // Backdrop Filters
      // =============================================================================
      
      backdropBlur: {
        xs: '2px',
      },
      
      backdropBrightness: {
        25: '.25',
        175: '1.75',
      },
      
      // =============================================================================
      // Grid Configuration
      // =============================================================================
      
      gridTemplateColumns: {
        '16': 'repeat(16, minmax(0, 1fr))',
        '20': 'repeat(20, minmax(0, 1fr))',
        'sidebar': '240px 1fr',
        'sidebar-collapsed': '64px 1fr',
        'dashboard': '1fr 320px',
        'analysis': '1fr 2fr',
      },
      
      gridTemplateRows: {
        '7': 'repeat(7, minmax(0, 1fr))',
        '8': 'repeat(8, minmax(0, 1fr))',
        'layout': 'auto 1fr auto',
        'dashboard': 'auto 1fr',
      },
      
      // =============================================================================
      // Z-Index Scale
      // =============================================================================
      
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      },
      
      // =============================================================================
      // Aspect Ratios
      // =============================================================================
      
      aspectRatio: {
        'chart': '16 / 10',
        'map': '4 / 3',
        'vessel': '3 / 2',
      },
    },
  },
  
  // =============================================================================
  // Plugins
  // =============================================================================
  
  plugins: [
    // Typography plugin for rich text content
    require('@tailwindcss/typography'),
    
    // Forms plugin for better form styling
    require('@tailwindcss/forms'),
    
    // Aspect ratio plugin
    require('@tailwindcss/aspect-ratio'),
    
    // Container queries plugin
    require('@tailwindcss/container-queries'),
    
    // Custom plugins
    function({ addUtilities, addComponents, theme }) {
      // Custom utilities
      const newUtilities = {
        // Glass morphism effects
        '.glass': {
          'backdrop-filter': 'blur(16px) saturate(180%)',
          'background-color': 'rgba(255, 255, 255, 0.75)',
          'border': '1px solid rgba(255, 255, 255, 0.125)',
        },
        '.glass-dark': {
          'backdrop-filter': 'blur(16px) saturate(180%)',
          'background-color': 'rgba(15, 23, 42, 0.75)',
          'border': '1px solid rgba(255, 255, 255, 0.125)',
        },
        
        // Gradient text
        '.text-gradient': {
          'background': 'linear-gradient(135deg, #3b82f6 0%, #14b8a6 100%)',
          '-webkit-background-clip': 'text',
          'background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        },
        
        // Maritime gradients
        '.bg-ocean-gradient': {
          'background': 'linear-gradient(135deg, #1e40af 0%, #0f172a 100%)',
        },
        '.bg-wave-gradient': {
          'background': 'linear-gradient(45deg, #3b82f6 0%, #14b8a6 50%, #059669 100%)',
        },
        
        // Scrollbar styling
        '.scrollbar-thin': {
          'scrollbar-width': 'thin',
          'scrollbar-color': theme('colors.neutral.400') + ' ' + theme('colors.neutral.100'),
        },
        '.scrollbar-none': {
          'scrollbar-width': 'none',
          '-ms-overflow-style': 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        },
      };
      
      // Custom components
      const newComponents = {
        // Card component
        '.card': {
          'background-color': theme('colors.white'),
          'border-radius': theme('borderRadius.lg'),
          'box-shadow': theme('boxShadow.card'),
          'padding': theme('spacing.6'),
          'border': `1px solid ${theme('colors.border.DEFAULT')}`,
        },
        '.card-dark': {
          'background-color': theme('colors.neutral.800'),
          'border-color': theme('colors.border.dark'),
          'color': theme('colors.white'),
        },
        
        // Button component base
        '.btn': {
          'display': 'inline-flex',
          'align-items': 'center',
          'justify-content': 'center',
          'border-radius': theme('borderRadius.md'),
          'font-weight': theme('fontWeight.medium'),
          'transition': 'all 0.2s ease-in-out',
          'cursor': 'pointer',
          'user-select': 'none',
          '&:disabled': {
            'opacity': '0.6',
            'cursor': 'not-allowed',
          },
        },
        
        // Maritime-themed containers
        '.vessel-card': {
          'background': 'linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(20, 184, 166, 0.05) 100%)',
          'border': `1px solid ${theme('colors.primary.200')}`,
          'border-radius': theme('borderRadius.xl'),
          'padding': theme('spacing.6'),
          'position': 'relative',
          'overflow': 'hidden',
          '&::before': {
            'content': '""',
            'position': 'absolute',
            'top': '0',
            'left': '0',
            'right': '0',
            'height': '2px',
            'background': `linear-gradient(90deg, ${theme('colors.primary.500')} 0%, ${theme('colors.secondary.500')} 100%)`,
          },
        },
      };
      
      addUtilities(newUtilities);
      addComponents(newComponents);
    },
  ],
  
  // =============================================================================
  // Compatibility & Prefixes
  // =============================================================================
  
  // Prefix for CSS classes (if needed to avoid conflicts)
  prefix: '',
  
  // Important strategy
  important: false,
  
  // Separator for variant modifiers
  separator: ':',
  
  // =============================================================================
  // CorePlugins Configuration
  // =============================================================================
  
  corePlugins: {
    // Disable plugins that might conflict with Material-UI
    preflight: true, // Keep Tailwind's CSS reset
    container: true,
    accessibility: true,
    pointerEvents: true,
    visibility: true,
    position: true,
    inset: true,
    isolation: true,
    zIndex: true,
    order: true,
    gridColumn: true,
    gridColumnStart: true,
    gridColumnEnd: true,
    gridRow: true,
    gridRowStart: true,
    gridRowEnd: true,
    float: true,
    clear: true,
    margin: true,
    boxSizing: true,
    display: true,
    aspectRatio: true,
    height: true,
    maxHeight: true,
    minHeight: true,
    width: true,
    minWidth: true,
    maxWidth: true,
    flex: true,
    flexShrink: true,
    flexGrow: true,
    flexBasis: true,
    tableLayout: true,
    borderCollapse: true,
    transformOrigin: true,
    transform: true,
    animation: true,
    cursor: true,
    userSelect: true,
    resize: true,
    scrollSnapType: true,
    scrollSnapAlign: true,
    scrollSnapStop: true,
    scrollMargin: true,
    scrollPadding: true,
    listStyleType: true,
    listStylePosition: true,
    appearance: true,
    columns: true,
    breakBefore: true,
    breakInside: true,
    breakAfter: true,
    gridAutoColumns: true,
    gridAutoFlow: true,
    gridAutoRows: true,
    gridTemplateColumns: true,
    gridTemplateRows: true,
    flexDirection: true,
    flexWrap: true,
    placeContent: true,
    placeItems: true,
    alignContent: true,
    alignItems: true,
    justifyContent: true,
    justifyItems: true,
    gap: true,
    space: true,
    divideWidth: true,
    divideStyle: true,
    divideColor: true,
    divideOpacity: true,
    placeSelf: true,
    alignSelf: true,
    justifySelf: true,
    overflow: true,
    overscrollBehavior: true,
    scrollBehavior: true,
    textOverflow: true,
    whitespace: true,
    wordBreak: true,
    borderRadius: true,
    borderWidth: true,
    borderStyle: true,
    borderColor: true,
    borderOpacity: true,
    backgroundColor: true,
    backgroundOpacity: true,
    backgroundImage: true,
    gradientColorStops: true,
    backgroundSize: true,
    backgroundAttachment: true,
    backgroundClip: true,
    backgroundPosition: true,
    backgroundRepeat: true,
    backgroundOrigin: true,
    fill: true,
    stroke: true,
    strokeWidth: true,
    objectFit: true,
    objectPosition: true,
    padding: true,
    textAlign: true,
    textColor: true,
    textOpacity: true,
    textDecoration: true,
    textDecorationColor: true,
    textDecorationStyle: true,
    textDecorationThickness: true,
    textUnderlineOffset: true,
    textTransform: true,
    textIndent: true,
    verticalAlign: true,
    fontFamily: true,
    fontSize: true,
    fontWeight: true,
    textRendering: true,
  }
  }
};